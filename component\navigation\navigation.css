/* 导航容器 - 浮动导航栏 */
#navigation-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background-color: transparent;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 导航占位空间 - 用于抵消浮动导航栏脱离文档流的影响 */
.navigation-placeholder {
	height: 184px; /* 主导航92px + 二级导航92px */
	background-color: #ffffff;
}

/* 加载错误样式 */
.navigation-error {
	height: 184px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 14px;
}

/* 顶部导航栏 */
.header {
	display: flex;
	align-items: center;
	width: 100%;
	height: 92px;
}

.header-left {
	flex: 1;
	background-color: transparent;
	height: 100%;
}

.header-center {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 1025px;
	height: 100%;
	background-color: transparent;
}

.header-right {
	flex: 1;
	background-color: #ffffff;
	height: 100%;
}

.logo-section {
	display: flex;
	align-items: center;
	gap: 12px;
	height: 100%;
	padding-left: 24px;
	padding-right: 24px;
	background-color: transparent;
}

.logo-icon {
	width: 42px;
	height: 42px;
	border-radius: 50%;
	background-image: url(../../assets/home/<USER>/79b144a48e324afe8d417c938894a984_mergeImage.png);
	background-size: cover;
}

.company-name {
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	color: #000000;
	margin: 0;
	line-height: 42px;
}

.main-nav {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	flex: 1;
	background-color: #ffffff;
	height: 100%;
	/* gap: 40px; */
}

.nav-link {
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	text-decoration: none;
	text-align: center;
	line-height: 92px;
	width: 100px;
}

.nav-link:hover {
	background: #0e6be4;
	color: #fff;
}

.nav-link.active {
	background: #0055c3;
	color: #fff;
	font-weight: 500;
}

.nav-icon {
	width: 14px;
	height: 14px;
	margin-left: 40px;
}

/* 二级导航栏 */
.secondary-nav {
	display: flex;
	align-items: center;
	width: 100%;
	height: 92px;
	background-color: transparent;
}

.secondary-nav-left {
	flex: 1;
	background-color: transparent;
	height: 100%;
}

.secondary-nav-right {
	flex: 1;
	background-color: #090909;
	height: 100%;
}

.category-highlight {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 118px;
	height: 92px;
	background-color: #0055c3;
	box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
}

.category-name {
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #ffffff;
	line-height: 25px;
}

.product-nav {
	display: flex;
	align-items: center;
	background-color: #090909;
	height: 92px;
	width: 1025px;
	position: relative;
}

.product-link {
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #ffffff;
	text-decoration: none;
	line-height: 92px;
	width: 135px;
	text-align: center;
}

.product-link:hover {
	background: #0e6be4;
	color: #fff;
}

.product-link.active {
	background: #0055c3;
	color: #fff;
	font-weight: 500;
}

.contact-sales-btn {
	background-color: #ffffff;
	color: #000000;
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	line-height: 30px;
	border: none;
	padding: 31px 26px;
	margin-left: auto;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.contact-sales-btn:hover {
	background-color: #f0f0f0;
}

.nav-divider {
	position: absolute;
	left: -84px;
	top: 45px;
	width: 94px;
	height: 1px;
}
